#!/usr/bin/env python3
"""
Live Market Paper Trading for Project Chimera
Uses REAL market data from Binance WebSocket + CoinGecko
"""

import os
import sys
import time
import json
import threading
from pathlib import Path
from datetime import datetime, timedelta
from decimal import Decimal
import logging

# Set paper trading mode and environment
os.environ['PAPER_TRADING_MODE'] = 'true'
os.environ['MONITORING_INTERVAL_SECONDS'] = '10'  # Check every 10 seconds for live data

# Set default configuration
os.environ.setdefault('PRESSURE_SCORE_THRESHOLD', '0.75')
os.environ.setdefault('STOP_LOSS_PCT', '0.15')
os.environ.setdefault('TAKE_PROFIT_PCT', '0.10')
os.environ.setdefault('BORROW_AMOUNT_PER_TRADE', '1000')
os.environ.setdefault('TAKE_PROFIT_DAYS_BEFORE_UNLOCK', '1')

# Mock Redis URL for local testing
if not os.environ.get('REDIS_URL'):
    os.environ['REDIS_URL'] = 'redis://localhost:6379'

# Add service paths
for service in ['the-oracle', 'the-seer', 'the-executioner', 'the-ledger', 'the-herald']:
    sys.path.insert(0, str(Path(__file__).parent / 'services' / service))

class LiveMarketPaperTrader:
    """Live paper trading with real market data"""
    
    def __init__(self):
        self.running = False
        self.positions = []
        self.monitoring_thread = None
        self.session_start_time = datetime.now()
        self.price_feed = None
        
        # Set up logging to reduce noise
        logging.basicConfig(level=logging.WARNING)
        
    def start_live_session(self):
        """Start live paper trading session with real market data"""
        print("🚀 Project Chimera - LIVE Market Paper Trading")
        print("=" * 60)
        print("📡 Using REAL market data from Binance WebSocket")
        print("🧪 Paper trading mode - NO REAL MONEY AT RISK")
        print("🎯 Testing with live market conditions")
        
        # Initialize systems
        if not self.initialize_systems():
            return
        
        # Create realistic scenarios with current market data
        print("\n🔮 Step 1: Analyzing Current Market Conditions...")
        unlock_events = self.create_market_scenarios()
        
        # Run strategy analysis
        print("\n🧠 Step 2: Running Strategy Analysis...")
        trade_candidates = self.analyze_with_real_data(unlock_events)
        
        # Execute trades
        print("\n⚔️ Step 3: Executing Paper Trades...")
        self.execute_trades(trade_candidates)
        
        # Start live monitoring
        print("\n📊 Step 4: Starting LIVE Market Monitoring...")
        self.start_live_monitoring()
        
        # Run session
        self.run_live_session()
    
    def initialize_systems(self):
        """Initialize paper trading and price feed systems"""
        try:
            # Initialize paper trading engine
            from paper_trading import paper_engine
            self.paper_engine = paper_engine
            
            # Initialize Binance price feed
            from binance_websocket import BinanceWebSocketPriceFeed
            self.price_feed = BinanceWebSocketPriceFeed(use_testnet=False)
            
            portfolio = self.paper_engine.get_portfolio_summary()
            print(f"💰 Initial Portfolio: {portfolio['balances']}")
            
            print("📡 Initializing live price feeds...")
            return True
            
        except Exception as e:
            print(f"❌ System initialization failed: {e}")
            return False
    
    def create_market_scenarios(self):
        """Create scenarios using current market data"""
        from datetime import timezone
        
        # Real tokens with current market activity
        scenarios = [
            {
                'token_symbol': 'UNI',
                'contract_address': '0x1f9840a85d5af5bf1d1762f925bdaddc4201f984',
                'unlock_date': (datetime.now() + timedelta(days=30)).isoformat() + 'Z',
                'unlock_amount': 50000000,  # 50M tokens
                'circulating_supply': 750000000,  # 750M circulating
                'total_supply': 1000000000,  # 1B total
                'volume_24h': 200000000,  # $200M daily volume
                'source': 'Live_Market_Data'
            },
            {
                'token_symbol': 'COMP',
                'contract_address': '0xc00e94cb662c3520282e6f5717214004a7f26888',
                'unlock_date': (datetime.now() + timedelta(days=25)).isoformat() + 'Z',
                'unlock_amount': 15000000,  # 15M tokens
                'circulating_supply': 100000000,  # 100M circulating
                'total_supply': 10000000,  # 10M total
                'volume_24h': 60000000,  # $60M daily volume
                'source': 'Live_Market_Data'
            },
            {
                'token_symbol': 'AAVE',
                'contract_address': '0x7fc66500c84a76ad7e9c93437bfc5ac33e2ddae9',
                'unlock_date': (datetime.now() + timedelta(days=40)).isoformat() + 'Z',
                'unlock_amount': 3000000,  # 3M tokens
                'circulating_supply': 15000000,  # 15M circulating
                'total_supply': 16000000,  # 16M total
                'volume_24h': 150000000,  # $150M daily volume
                'source': 'Live_Market_Data'
            }
        ]
        
        print(f"📅 Created {len(scenarios)} market scenarios:")
        for scenario in scenarios:
            unlock_dt = datetime.fromisoformat(scenario['unlock_date'].replace('Z', '+00:00'))
            now_dt = datetime.now(timezone.utc)
            days_to_unlock = (unlock_dt - now_dt).days
            
            # Get current market price
            from price_fetcher import get_realtime_price
            current_price = get_realtime_price(scenario['contract_address'])
            
            if current_price:
                scenario['current_market_price'] = float(current_price)
                print(f"   🪙 {scenario['token_symbol']}: ${current_price:.2f} (unlock in {days_to_unlock} days)")
            else:
                print(f"   🪙 {scenario['token_symbol']}: Price unavailable (unlock in {days_to_unlock} days)")
            
            print(f"      📊 {scenario['unlock_amount']:,} tokens ({scenario['unlock_amount']/scenario['circulating_supply']*100:.1f}% of supply)")
        
        return scenarios
    
    def analyze_with_real_data(self, scenarios):
        """Analyze scenarios with real market data"""
        try:
            from analysis import calculate_unlock_pressure_score
            
            trade_candidates = []
            threshold = float(os.environ.get('PRESSURE_SCORE_THRESHOLD', '0.75'))
            
            print(f"🎯 Analyzing with threshold: {threshold}")
            
            for scenario in scenarios:
                print(f"\n🔍 Analyzing {scenario['token_symbol']} with LIVE data...")
                
                # Calculate pressure score with real market data
                score = calculate_unlock_pressure_score(scenario)
                
                # Get current market price for context
                current_price = scenario.get('current_market_price')
                price_info = f"${current_price:.2f}" if current_price else "Price unavailable"
                
                print(f"   📊 Pressure Score: {score:.4f}")
                print(f"   💰 Current Price: {price_info}")
                print(f"   📈 Unlock Impact: {scenario['unlock_amount']/scenario['circulating_supply']*100:.1f}% of supply")
                
                if score > threshold:
                    candidate = {**scenario, 'pressure_score': score}
                    trade_candidates.append(candidate)
                    print(f"   ✅ TRADE CANDIDATE: High conviction (score: {score:.4f})")
                else:
                    print(f"   ❌ Below threshold: {score:.4f} < {threshold}")
            
            print(f"\n🎯 Analysis Complete: {len(trade_candidates)} candidates for live trading")
            return trade_candidates
            
        except Exception as e:
            print(f"❌ Analysis error: {e}")
            return []
    
    def execute_trades(self, candidates):
        """Execute paper trades with real market prices"""
        try:
            from paper_trading import execute_paper_trade
            
            print(f"💼 Executing {len(candidates)} trades with LIVE market prices...")
            
            for candidate in candidates:
                print(f"\n📈 Opening position: {candidate['token_symbol']}")
                
                try:
                    # Subscribe to price feed for this token
                    self.price_feed.subscribe_to_price(candidate['contract_address'])
                    
                    # Execute paper trade
                    position = execute_paper_trade(candidate)
                    
                    # Add live market data
                    position['contract_address'] = candidate['contract_address']
                    position['live_monitoring'] = True
                    
                    self.positions.append(position)
                    
                    print(f"   ✅ Position opened: ID {position['position_id']}")
                    print(f"   💰 Entry price: ${position['entry_price_in_usdc']:.4f}")
                    
                    if 'current_market_price' in candidate:
                        print(f"   📊 Market price: ${candidate['current_market_price']:.2f}")
                    
                except Exception as e:
                    print(f"   ❌ Failed to open position: {e}")
            
            print(f"\n📊 Trade Execution Complete: {len(self.positions)} live positions")
            
        except Exception as e:
            print(f"❌ Execution error: {e}")
    
    def start_live_monitoring(self):
        """Start live market monitoring"""
        self.running = True
        self.monitoring_thread = threading.Thread(target=self.monitor_live_positions)
        self.monitoring_thread.daemon = True
        self.monitoring_thread.start()
        print("🔄 LIVE market monitoring started")
    
    def monitor_live_positions(self):
        """Monitor positions with REAL market data"""
        try:
            from risk_manager import check_risk_rules
            from price_fetcher import get_realtime_price
            
            while self.running:
                open_positions = [p for p in self.positions if p.get('status') == 'OPEN']
                
                if not open_positions:
                    time.sleep(2)
                    continue
                
                print(f"\n📊 Monitoring {len(open_positions)} positions with LIVE data...")
                
                for position in open_positions:
                    try:
                        # Get REAL market price
                        current_price = get_realtime_price(position.get('contract_address', position['token_address']))
                        
                        if current_price:
                            # Calculate P&L with real price
                            entry_price = Decimal(str(position['entry_price_in_usdc']))
                            pnl_pct = ((entry_price - current_price) / entry_price) * 100  # For shorts
                            
                            # Price change from entry
                            price_change_pct = ((current_price - entry_price) / entry_price) * 100
                            change_indicator = "📈" if price_change_pct > 0 else "📉" if price_change_pct < 0 else "➡️"
                            
                            print(f"   📡 {position['token_symbol']}: ${current_price:.4f} {change_indicator} {price_change_pct:+.2f}% (P&L: {pnl_pct:+.2f}%)")
                            
                            # Check risk rules with REAL price
                            action, reason = check_risk_rules(position, current_price)
                            
                            if action == "CLOSE":
                                print(f"   🚨 CLOSING: {reason}")
                                self.close_position(position, current_price, reason)
                            else:
                                print(f"   ✅ HOLDING: {reason}")
                        else:
                            print(f"   ⚠️ {position['token_symbol']}: Live price unavailable")
                    
                    except Exception as e:
                        print(f"   ❌ Error monitoring {position.get('token_symbol', 'Unknown')}: {e}")
                
                time.sleep(int(os.environ.get('MONITORING_INTERVAL_SECONDS', '10')))
                
        except Exception as e:
            print(f"❌ Live monitoring error: {e}")
    
    def close_position(self, position, current_price, reason):
        """Close a position with real market price"""
        try:
            from paper_trading import close_paper_position
            
            closed_position = close_paper_position(
                position['position_id'], 
                reason, 
                current_price
            )
            
            position['status'] = 'CLOSED'
            pnl = closed_position.get('pnl_usd', 0)
            print(f"   ✅ Position closed with REAL market P&L: ${pnl:.2f}")
            
        except Exception as e:
            print(f"   ❌ Error closing position: {e}")
    
    def run_live_session(self):
        """Run live trading session"""
        duration_minutes = 10  # 10-minute live session
        
        print(f"\n⏰ Running {duration_minutes}-minute LIVE market session...")
        print("📊 Real-time monitoring with live prices. Press Ctrl+C to stop early.")
        
        try:
            for minute in range(duration_minutes):
                print(f"\n⏱️ Minute {minute + 1}/{duration_minutes} - LIVE MARKET DATA")
                
                # Show portfolio with real market values
                portfolio = self.paper_engine.get_portfolio_summary()
                print(f"💰 Portfolio: {portfolio['balances']}")
                print(f"📈 Total P&L: ${portfolio['performance']['total_pnl_usd']:.2f}")
                
                open_count = len([p for p in self.positions if p.get('status') == 'OPEN'])
                closed_count = len([p for p in self.positions if p.get('status') == 'CLOSED'])
                print(f"📊 Positions: {open_count} open, {closed_count} closed")
                
                time.sleep(60)  # Wait 1 minute
                
        except KeyboardInterrupt:
            print("\n⏹️ Live session stopped by user")
        
        finally:
            self.stop_session()
    
    def stop_session(self):
        """Stop the live session"""
        print("\n🛑 Stopping LIVE market paper trading...")
        
        self.running = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=3)
        
        if self.price_feed:
            self.price_feed.stop()
        
        # Final summary with real market data
        portfolio = self.paper_engine.get_portfolio_summary()
        
        print("\n" + "=" * 60)
        print("📋 LIVE MARKET PAPER TRADING SESSION SUMMARY")
        print("=" * 60)
        print(f"⏱️ Session Duration: {(datetime.now() - self.session_start_time).total_seconds()/60:.1f} minutes")
        print(f"💰 Final Balances: {portfolio['balances']}")
        print(f"📈 Total P&L (REAL market): ${portfolio['performance']['total_pnl_usd']:.2f}")
        print(f"📊 Total Trades: {portfolio['performance']['total_trades']}")
        
        if portfolio['performance']['total_trades'] > 0:
            win_rate = portfolio['performance']['winning_trades'] / portfolio['performance']['total_trades'] * 100
            print(f"🎯 Win Rate: {win_rate:.1f}% ({portfolio['performance']['winning_trades']}/{portfolio['performance']['total_trades']})")
        
        # Save session log
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = f"live_market_paper_trading_{timestamp}.json"
        
        session_data = {
            'session_type': 'LIVE_MARKET_PAPER_TRADING',
            'real_market_data': True,
            'session_summary': portfolio,
            'positions': self.positions,
            'session_duration_minutes': (datetime.now() - self.session_start_time).total_seconds()/60,
            'timestamp': timestamp
        }
        
        with open(log_file, 'w') as f:
            json.dump(session_data, f, indent=2, default=str)
        
        print(f"📄 Live session log saved: {log_file}")
        print("\n✅ LIVE market paper trading session completed!")
        print("🎯 System validated with REAL market conditions!")

def main():
    """Main entry point"""
    print("🎯 Project Chimera - LIVE Market Paper Trading")
    print("📡 Real market data • 🧪 Paper trading safety")
    
    trader = LiveMarketPaperTrader()
    trader.start_live_session()

if __name__ == "__main__":
    main()
