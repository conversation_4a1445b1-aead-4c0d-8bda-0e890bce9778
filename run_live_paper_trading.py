#!/usr/bin/env python3
"""
Live Paper Trading Runner for Project Chimera
Runs the complete system with real market data in paper trading mode
"""

import os
import sys
import time
import json
import threading
from pathlib import Path
from datetime import datetime, timedelta
from decimal import Decimal

# Set paper trading mode and environment
os.environ['PAPER_TRADING_MODE'] = 'true'
os.environ['MONITORING_INTERVAL_SECONDS'] = '30'  # Check every 30 seconds for demo

# Set default configuration if not provided
os.environ.setdefault('PRESSURE_SCORE_THRESHOLD', '0.75')
os.environ.setdefault('STOP_LOSS_PCT', '0.15')
os.environ.setdefault('TAKE_PROFIT_PCT', '0.10')
os.environ.setdefault('BORROW_AMOUNT_PER_TRADE', '1000')
os.environ.setdefault('TAKE_PROFIT_DAYS_BEFORE_UNLOCK', '1')

# Mock Redis URL for local testing (services will handle gracefully)
if not os.environ.get('REDIS_URL'):
    os.environ['REDIS_URL'] = 'redis://localhost:6379'

# Add service paths
for service in ['the-oracle', 'the-seer', 'the-executioner', 'the-ledger', 'the-herald']:
    sys.path.insert(0, str(Path(__file__).parent / 'services' / service))

class LivePaperTradingRunner:
    """Orchestrates live paper trading with real market data"""
    
    def __init__(self):
        self.running = False
        self.positions = []
        self.monitoring_thread = None
        
    def start_live_paper_trading(self):
        """Start the live paper trading system"""
        print("🚀 Starting Live Paper Trading System")
        print("=" * 60)
        
        # Initialize paper trading engine
        self.initialize_paper_engine()
        
        # Step 1: Run Oracle to fetch real unlock data
        print("\n🔮 Step 1: Running Oracle - Fetching Real Market Data...")
        unlock_events = self.run_oracle()
        
        if not unlock_events:
            print("⚠️ No unlock events found. Creating demo scenario...")
            unlock_events = self.create_demo_unlock_events()
        
        # Step 2: Run Seer analysis on real data
        print("\n🧠 Step 2: Running Seer - Analyzing Unlock Events...")
        trade_candidates = self.run_seer_analysis(unlock_events)
        
        # Step 3: Execute trades via Executioner
        print("\n⚔️ Step 3: Running Executioner - Opening Positions...")
        self.execute_trades(trade_candidates)
        
        # Step 4: Start continuous monitoring via Ledger
        print("\n📊 Step 4: Starting Ledger - Continuous Risk Monitoring...")
        self.start_position_monitoring()
        
        # Step 5: Run for specified duration
        self.run_trading_session()
        
    def initialize_paper_engine(self):
        """Initialize the paper trading engine"""
        try:
            from paper_trading import paper_engine
            self.paper_engine = paper_engine
            
            # Reset engine for fresh start
            self.paper_engine.reset_portfolio()
            
            portfolio = self.paper_engine.get_portfolio_summary()
            print(f"💰 Initial Portfolio: {portfolio['balances']}")
            
        except Exception as e:
            print(f"❌ Failed to initialize paper engine: {e}")
            return False
        
        return True
    
    def run_oracle(self):
        """Run Oracle to fetch real unlock data"""
        try:
            from data_sources import fetch_token_unlocks_data
            
            print("📡 Connecting to real market data sources...")
            events = fetch_token_unlocks_data()
            
            print(f"✅ Oracle found {len(events)} unlock events")
            for event in events[:3]:  # Show first 3
                print(f"   📅 {event.get('token_symbol', 'Unknown')}: {event.get('unlock_date', 'Unknown')}")
            
            return events
            
        except Exception as e:
            print(f"❌ Oracle error: {e}")
            return []
    
    def create_demo_unlock_events(self):
        """Create demo unlock events with real tokens for testing"""
        future_date = (datetime.now() + timedelta(days=30)).isoformat() + 'Z'
        
        demo_events = [
            {
                'token_symbol': 'UNI',
                'contract_address': '0x1f9840a85d5aF5bf1D1762F925BDADdC4201F984',
                'unlock_date': future_date,
                'unlock_amount': 50000000,
                'circulating_supply': 750000000,
                'total_supply': 1000000000,
                'source': 'Demo_Data'
            },
            {
                'token_symbol': 'AAVE',
                'contract_address': '0x7Fc66500c84A76Ad7e9c93437bFc5Ac33E2DDaE9',
                'unlock_date': (datetime.now() + timedelta(days=45)).isoformat() + 'Z',
                'unlock_amount': 25000000,
                'circulating_supply': 14000000,
                'total_supply': 16000000,
                'source': 'Demo_Data'
            }
        ]
        
        print(f"🎭 Created {len(demo_events)} demo unlock events")
        return demo_events
    
    def run_seer_analysis(self, unlock_events):
        """Run Seer analysis on unlock events"""
        try:
            from analysis import calculate_unlock_pressure_score
            
            trade_candidates = []
            threshold = float(os.environ.get('PRESSURE_SCORE_THRESHOLD', '0.75'))
            
            for event in unlock_events:
                print(f"\n🔍 Analyzing {event['token_symbol']}...")
                
                # Calculate pressure score with real market data
                score = calculate_unlock_pressure_score(event)
                print(f"   📊 Pressure Score: {score:.4f}")
                
                if score > threshold:
                    candidate = {**event, 'pressure_score': score}
                    trade_candidates.append(candidate)
                    print(f"   ✅ TRADE CANDIDATE: Score {score:.4f} > {threshold}")
                else:
                    print(f"   ❌ Below threshold: {score:.4f} < {threshold}")
            
            print(f"\n🎯 Seer identified {len(trade_candidates)} trade candidates")
            return trade_candidates
            
        except Exception as e:
            print(f"❌ Seer analysis error: {e}")
            return []
    
    def execute_trades(self, trade_candidates):
        """Execute trades via Executioner"""
        try:
            from paper_trading import execute_paper_trade
            
            for candidate in trade_candidates:
                print(f"\n💼 Opening position for {candidate['token_symbol']}...")
                
                try:
                    position = execute_paper_trade(candidate)
                    self.positions.append(position)
                    
                    print(f"   ✅ Position opened: ID {position['position_id']}")
                    print(f"   💰 Entry price: ${position['entry_price_in_usdc']:.4f}")
                    
                except Exception as e:
                    print(f"   ❌ Failed to open position: {e}")
            
            print(f"\n📈 Executioner opened {len(self.positions)} positions")
            
        except Exception as e:
            print(f"❌ Executioner error: {e}")
    
    def start_position_monitoring(self):
        """Start continuous position monitoring"""
        self.running = True
        self.monitoring_thread = threading.Thread(target=self.monitor_positions)
        self.monitoring_thread.daemon = True
        self.monitoring_thread.start()
        
        print("🔄 Position monitoring started (background thread)")
    
    def monitor_positions(self):
        """Continuous position monitoring loop"""
        try:
            from risk_manager import check_risk_rules
            from price_fetcher import get_realtime_price
            
            while self.running:
                if not self.positions:
                    time.sleep(5)
                    continue
                
                print(f"\n📊 Monitoring {len(self.positions)} positions...")
                
                for position in self.positions[:]:  # Copy list to avoid modification issues
                    if position.get('status') != 'OPEN':
                        continue
                    
                    try:
                        # Get real-time price
                        current_price = get_realtime_price(position['token_address'])
                        
                        if current_price:
                            # Check risk rules
                            action, reason = check_risk_rules(position, current_price)
                            
                            print(f"   🔍 {position['token_symbol']}: ${current_price:.4f} - {action}")
                            
                            if action == "CLOSE":
                                print(f"   🚨 CLOSING: {reason}")
                                self.close_position(position, current_price, reason)
                        else:
                            print(f"   ⚠️ Could not fetch price for {position['token_symbol']}")
                    
                    except Exception as e:
                        print(f"   ❌ Error monitoring {position.get('token_symbol', 'Unknown')}: {e}")
                
                # Wait before next check
                time.sleep(int(os.environ.get('MONITORING_INTERVAL_SECONDS', '30')))
                
        except Exception as e:
            print(f"❌ Monitoring error: {e}")
    
    def close_position(self, position, current_price, reason):
        """Close a position"""
        try:
            from paper_trading import close_paper_position
            
            closed_position = close_paper_position(
                position['position_id'], 
                reason, 
                current_price
            )
            
            # Update position status
            position['status'] = 'CLOSED'
            
            pnl = closed_position.get('pnl_usd', 0)
            print(f"   ✅ Position closed with P&L: ${pnl:.2f}")
            
        except Exception as e:
            print(f"   ❌ Error closing position: {e}")
    
    def run_trading_session(self):
        """Run the trading session for a specified duration"""
        duration_minutes = 10  # Run for 10 minutes as demo
        
        print(f"\n⏰ Running live paper trading session for {duration_minutes} minutes...")
        print("📊 Real-time monitoring active. Press Ctrl+C to stop early.")
        
        try:
            for minute in range(duration_minutes):
                print(f"\n⏱️ Minute {minute + 1}/{duration_minutes}")
                
                # Show portfolio summary
                portfolio = self.paper_engine.get_portfolio_summary()
                print(f"💰 Portfolio: {portfolio['balances']}")
                print(f"📈 P&L: ${portfolio['performance']['total_pnl_usd']:.2f}")
                print(f"📊 Positions: {portfolio['positions']['open']} open, {portfolio['positions']['closed']} closed")
                
                time.sleep(60)  # Wait 1 minute
                
        except KeyboardInterrupt:
            print("\n⏹️ Trading session stopped by user")
        
        finally:
            self.stop_trading()
    
    def stop_trading(self):
        """Stop the trading system"""
        print("\n🛑 Stopping live paper trading system...")
        
        self.running = False
        
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        
        # Final portfolio summary
        portfolio = self.paper_engine.get_portfolio_summary()
        
        print("\n" + "=" * 60)
        print("📋 FINAL TRADING SESSION SUMMARY")
        print("=" * 60)
        print(f"💰 Final Balances: {portfolio['balances']}")
        print(f"📈 Total P&L: ${portfolio['performance']['total_pnl_usd']:.2f}")
        print(f"📊 Total Trades: {portfolio['performance']['total_trades']}")
        print(f"🎯 Win Rate: {portfolio['performance']['winning_trades']}/{portfolio['performance']['total_trades']}")
        
        # Save trading log
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = f"live_paper_trading_log_{timestamp}.json"
        
        with open(log_file, 'w') as f:
            json.dump(portfolio, f, indent=2, default=str)
        
        print(f"📄 Trading log saved: {log_file}")
        print("\n✅ Live paper trading session completed!")

def main():
    """Main entry point"""
    print("🎯 Project Chimera - Live Paper Trading")
    print("🧪 Running with REAL market data in SAFE paper mode")
    print("💡 No real money at risk - all trades are simulated")
    
    runner = LivePaperTradingRunner()
    runner.start_live_paper_trading()

if __name__ == "__main__":
    main()
